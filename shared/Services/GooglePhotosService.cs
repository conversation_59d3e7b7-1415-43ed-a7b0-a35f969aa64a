using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.IO;
using System.Net.Http;
using System.Threading.Tasks;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace VidCompressor.Services;

public class GooglePhotosService
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<GooglePhotosService> _logger;

    public GooglePhotosService(IHttpClientFactory httpClientFactory, ILogger<GooglePhotosService> logger)
    {
        _httpClient = httpClientFactory.CreateClient();
        _logger = logger;
    }

    public async Task<List<string>> GetVideosAsync(string accessToken)
    {
        var request = new HttpRequestMessage(HttpMethod.Get, "https://photoslibrary.googleapis.com/v1/mediaItems");
        request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

        var response = await _httpClient.SendAsync(request);
        response.EnsureSuccessStatusCode();

        var content = await response.Content.ReadAsStringAsync();
        var mediaResponse = JsonSerializer.Deserialize<MediaItemsResponse>(content);

        var videos = new List<string>();
        if (mediaResponse?.MediaItems != null)
        {
            foreach (var item in mediaResponse.MediaItems)
            {
                if (item.MediaMetadata?.Video != null)
                {
                    videos.Add(item.Id);
                }
            }
        }

        return videos;
    }

    public async Task<VideoInfo> GetVideoInfoAsync(string accessToken, string mediaItemId)
    {
        var request = new HttpRequestMessage(HttpMethod.Get, $"https://photoslibrary.googleapis.com/v1/mediaItems/{mediaItemId}");
        request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

        var response = await _httpClient.SendAsync(request);
        response.EnsureSuccessStatusCode();

        var content = await response.Content.ReadAsStringAsync();
        var mediaItem = JsonSerializer.Deserialize<MediaItem>(content);

        if (mediaItem == null)
        {
            throw new InvalidOperationException($"Media item with ID {mediaItemId} not found");
        }

        var videoMetadata = mediaItem.MediaMetadata?.Video;
        if (videoMetadata == null)
        {
            throw new InvalidOperationException($"Media item {mediaItemId} is not a video");
        }

        // Calculate file size estimate
        var width = int.Parse(mediaItem.MediaMetadata?.Width ?? "1920");
        var height = int.Parse(mediaItem.MediaMetadata?.Height ?? "1080");
        var fps = videoMetadata.Fps > 0 ? videoMetadata.Fps : 30.0;

        var durationSeconds = 60.0; // Default estimate
        var estimatedBitrate = (width * height * fps) / 250000.0;
        var estimatedSizeBytes = (long)(estimatedBitrate * durationSeconds * 125000);

        return new VideoInfo
        {
            MediaItemId = mediaItemId,
            Width = width,
            Height = height,
            Duration = durationSeconds,
            EstimatedSizeBytes = estimatedSizeBytes,
            Filename = mediaItem.Filename ?? "video.mp4"
        };
    }

    public async Task<Stream> DownloadVideoAsync(string accessToken, string mediaItemId, string? baseUrl = null)
    {
        _logger.LogInformation("Downloading video {MediaItemId}", mediaItemId);
        _logger.LogInformation("Using access token: {TokenPrefix}...", accessToken?.Substring(0, Math.Min(10, accessToken?.Length ?? 0)));

        string mediaBaseUrl;

        // If baseUrl is provided (from PhotosPicker API), use it directly
        if (!string.IsNullOrEmpty(baseUrl))
        {
            _logger.LogInformation("Using provided baseUrl from PhotosPicker API");
            mediaBaseUrl = baseUrl;
        }
        else
        {
            _logger.LogInformation("No baseUrl provided, falling back to Photos Library API (may fail for user-selected media)");

            // Fallback to Photos Library API (this will likely fail for user-selected media due to scope restrictions)
            var request = new HttpRequestMessage(HttpMethod.Get, $"https://photoslibrary.googleapis.com/v1/mediaItems/{mediaItemId}");
            request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            var response = await _httpClient.SendAsync(request);

            if (!response.IsSuccessStatusCode)
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogError("Failed to get media item. Status: {StatusCode}, Content: {Content}",
                    response.StatusCode, errorContent);
                throw new HttpRequestException($"Failed to get media item: {response.StatusCode} - {errorContent}");
            }

            var content = await response.Content.ReadAsStringAsync();
            var mediaItem = JsonSerializer.Deserialize<MediaItem>(content);

            if (mediaItem == null)
            {
                throw new InvalidOperationException($"Media item with ID {mediaItemId} not found");
            }

            if (string.IsNullOrEmpty(mediaItem.BaseUrl))
            {
                throw new InvalidOperationException($"No base URL available for media item {mediaItemId}");
            }

            mediaBaseUrl = mediaItem.BaseUrl;
        }

        // Download the video using the base URL with video parameters
        var downloadUrl = $"{mediaBaseUrl}=dv"; // =dv parameter for video download
        var downloadRequest = new HttpRequestMessage(HttpMethod.Get, downloadUrl);
        downloadRequest.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

        var downloadResponse = await _httpClient.SendAsync(downloadRequest);
        downloadResponse.EnsureSuccessStatusCode();

        _logger.LogInformation("Successfully downloaded video {MediaItemId}, size: {Size} bytes",
            mediaItemId, downloadResponse.Content.Headers.ContentLength);

        return await downloadResponse.Content.ReadAsStreamAsync();
    }

    public async Task<Stream> DownloadPhotoAsync(string accessToken, string mediaItemId, string? baseUrl = null)
    {
        _logger.LogInformation("Downloading photo {MediaItemId}", mediaItemId);
        _logger.LogInformation("Using access token: {TokenPrefix}...", accessToken?.Substring(0, Math.Min(10, accessToken?.Length ?? 0)));

        string mediaBaseUrl;

        // If baseUrl is provided (from PhotosPicker API), use it directly
        if (!string.IsNullOrEmpty(baseUrl))
        {
            _logger.LogInformation("Using provided baseUrl from PhotosPicker API");
            mediaBaseUrl = baseUrl;
        }
        else
        {
            _logger.LogInformation("No baseUrl provided, falling back to Photos Library API (may fail for user-selected media)");

            // Fallback to Photos Library API (this will likely fail for user-selected media due to scope restrictions)
            var request = new HttpRequestMessage(HttpMethod.Get, $"https://photoslibrary.googleapis.com/v1/mediaItems/{mediaItemId}");
            request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            var response = await _httpClient.SendAsync(request);

            if (!response.IsSuccessStatusCode)
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogError("Failed to get media item. Status: {StatusCode}, Content: {Content}",
                    response.StatusCode, errorContent);
                throw new HttpRequestException($"Failed to get media item: {response.StatusCode} - {errorContent}");
            }

            var content = await response.Content.ReadAsStringAsync();
            var mediaItem = JsonSerializer.Deserialize<MediaItem>(content);

            if (mediaItem == null)
            {
                throw new InvalidOperationException($"Media item with ID {mediaItemId} not found");
            }

            if (string.IsNullOrEmpty(mediaItem.BaseUrl))
            {
                throw new InvalidOperationException($"No base URL available for media item {mediaItemId}");
            }

            mediaBaseUrl = mediaItem.BaseUrl;
        }

        // Download the photo using the base URL with photo parameters
        var downloadUrl = $"{mediaBaseUrl}=d"; // =d parameter for photo download (original quality)
        var downloadRequest = new HttpRequestMessage(HttpMethod.Get, downloadUrl);
        downloadRequest.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

        var downloadResponse = await _httpClient.SendAsync(downloadRequest);
        downloadResponse.EnsureSuccessStatusCode();

        _logger.LogInformation("Successfully downloaded photo {MediaItemId}, size: {Size} bytes",
            mediaItemId, downloadResponse.Content.Headers.ContentLength);

        return await downloadResponse.Content.ReadAsStreamAsync();
    }

    public async Task UploadVideoAsync(string accessToken, string filePath)
    {
        _logger.LogInformation("Uploading video from {FilePath}", filePath);

        // Step 1: Upload the file to get an upload token
        var uploadRequest = new HttpRequestMessage(HttpMethod.Post, "https://photoslibrary.googleapis.com/v1/uploads");
        uploadRequest.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
        uploadRequest.Headers.Add("X-Goog-Upload-File-Name", Path.GetFileName(filePath));
        uploadRequest.Headers.Add("X-Goog-Upload-Protocol", "raw");

        var fileBytes = await File.ReadAllBytesAsync(filePath);
        uploadRequest.Content = new ByteArrayContent(fileBytes);
        uploadRequest.Content.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("application/octet-stream");

        var uploadResponse = await _httpClient.SendAsync(uploadRequest);
        uploadResponse.EnsureSuccessStatusCode();

        var uploadToken = await uploadResponse.Content.ReadAsStringAsync();

        // Step 2: Create the media item using the upload token
        var createRequest = new HttpRequestMessage(HttpMethod.Post, "https://photoslibrary.googleapis.com/v1/mediaItems:batchCreate");
        createRequest.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
        createRequest.Content = new StringContent(JsonSerializer.Serialize(new
        {
            newMediaItems = new[]
            {
                new
                {
                    description = "Compressed video",
                    simpleMediaItem = new
                    {
                        uploadToken = uploadToken
                    }
                }
            }
        }), System.Text.Encoding.UTF8, "application/json");

        var createResponse = await _httpClient.SendAsync(createRequest);
        createResponse.EnsureSuccessStatusCode();

        _logger.LogInformation("Successfully uploaded video from {FilePath}", filePath);
    }

    public async Task UploadPhotoAsync(string accessToken, string filePath)
    {
        _logger.LogInformation("Uploading photo from {FilePath}", filePath);

        // Step 1: Upload the file to get an upload token
        var uploadRequest = new HttpRequestMessage(HttpMethod.Post, "https://photoslibrary.googleapis.com/v1/uploads");
        uploadRequest.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
        uploadRequest.Headers.Add("X-Goog-Upload-File-Name", Path.GetFileName(filePath));
        uploadRequest.Headers.Add("X-Goog-Upload-Protocol", "raw");

        var fileBytes = await File.ReadAllBytesAsync(filePath);
        uploadRequest.Content = new ByteArrayContent(fileBytes);
        uploadRequest.Content.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("application/octet-stream");

        var uploadResponse = await _httpClient.SendAsync(uploadRequest);
        uploadResponse.EnsureSuccessStatusCode();

        var uploadToken = await uploadResponse.Content.ReadAsStringAsync();

        // Step 2: Create the media item using the upload token
        var createRequest = new HttpRequestMessage(HttpMethod.Post, "https://photoslibrary.googleapis.com/v1/mediaItems:batchCreate");
        createRequest.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
        createRequest.Content = new StringContent(JsonSerializer.Serialize(new
        {
            newMediaItems = new[]
            {
                new
                {
                    description = "Compressed photo",
                    simpleMediaItem = new
                    {
                        uploadToken = uploadToken
                    }
                }
            }
        }), System.Text.Encoding.UTF8, "application/json");

        var createResponse = await _httpClient.SendAsync(createRequest);
        createResponse.EnsureSuccessStatusCode();

        _logger.LogInformation("Successfully uploaded photo from {FilePath}", filePath);
    }
}

public class VideoInfo
{
    public string MediaItemId { get; set; } = string.Empty;
    public int Width { get; set; }
    public int Height { get; set; }
    public double Duration { get; set; }
    public long EstimatedSizeBytes { get; set; }
    public string Filename { get; set; } = string.Empty;
}

// JSON models for Google Photos API responses
public class MediaItemsResponse
{
    [JsonPropertyName("mediaItems")]
    public List<MediaItem> MediaItems { get; set; } = new();

    [JsonPropertyName("nextPageToken")]
    public string? NextPageToken { get; set; }
}

public class MediaItem
{
    [JsonPropertyName("id")]
    public string Id { get; set; } = string.Empty;

    [JsonPropertyName("filename")]
    public string Filename { get; set; } = string.Empty;

    [JsonPropertyName("baseUrl")]
    public string BaseUrl { get; set; } = string.Empty;

    [JsonPropertyName("mediaMetadata")]
    public MediaMetadata? MediaMetadata { get; set; }
}

public class MediaMetadata
{
    [JsonPropertyName("width")]
    public string Width { get; set; } = string.Empty;

    [JsonPropertyName("height")]
    public string Height { get; set; } = string.Empty;

    [JsonPropertyName("video")]
    public VideoMetadata? Video { get; set; }
}

public class VideoMetadata
{
    [JsonPropertyName("fps")]
    public double Fps { get; set; }

    [JsonPropertyName("processingStatus")]
    public string ProcessingStatus { get; set; } = string.Empty;
}

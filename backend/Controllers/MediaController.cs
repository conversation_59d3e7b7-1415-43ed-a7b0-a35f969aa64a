using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.SignalR;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;
using VidCompressor.Models;
using VidCompressor.Services;

namespace VidCompressor.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class MediaController : ControllerBase
{
    private readonly ApplicationDbContext _context;
    private readonly CloudTasksService _cloudTasksService;
    private readonly ILogger<MediaController> _logger;

    public MediaController(
        ApplicationDbContext context,
        CloudTasksService cloudTasksService,
        ILogger<MediaController> logger)
    {
        _context = context;
        _cloudTasksService = cloudTasksService;
        _logger = logger;
    }

    /// <summary>
    /// Initiates media compression (photos or videos) using appropriate compression service
    /// </summary>
    [HttpPost("{mediaItemId}/compress")]
    public async Task<IActionResult> CompressMedia(string mediaItemId, [FromBody] CompressionJobRequest request)
    {
        var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (userId == null) return Unauthorized();

        try
        {
            // Create compression job record
            var compressionJob = new CompressionJob
            {
                UserId = userId,
                MediaItemId = mediaItemId,
                MediaType = request.MediaType,
                Quality = request.Quality,
                UploadToGooglePhotos = request.UploadToGooglePhotos,
                OverwriteOriginal = request.OverwriteOriginal,
                BaseUrl = request.BaseUrl,
                OriginalWidth = request.OriginalWidth,
                OriginalHeight = request.OriginalHeight,
                Status = CompressionJobStatus.Queued
            };

            _context.CompressionJobs.Add(compressionJob);
            await _context.SaveChangesAsync();

            // Send initial status update via SignalR
            try
            {
                var hubContext = HttpContext.RequestServices.GetRequiredService<IHubContext<VidCompressor.Hubs.NotificationHub>>();
                await hubContext.Clients.All.SendAsync("CompressionStatusUpdate", new
                {
                    jobId = compressionJob.Id,
                    mediaItemId = compressionJob.MediaItemId,
                    status = "Queued",
                    message = "Job queued for processing",
                    progress = 0,
                    userId = userId
                });
                _logger.LogInformation("Sent initial SignalR update for job {JobId}", compressionJob.Id);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to send initial SignalR update for job {JobId}", compressionJob.Id);
            }

            // Queue the job for background processing
            await _cloudTasksService.EnqueueCompressionJobAsync(compressionJob.Id);

            var mediaTypeText = request.MediaType == MediaType.Photo ? "photo" : "video";
            return Ok(new CompressionJobResponse
            {
                JobId = compressionJob.Id,
                Status = compressionJob.Status.ToString(),
                Message = $"{char.ToUpper(mediaTypeText[0])}{mediaTypeText[1..]} compression job has been queued",
                CreatedAt = compressionJob.CreatedAt
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to queue compression job for media item {MediaItemId}", mediaItemId);
            return StatusCode(500, new { error = "Failed to queue compression job", details = ex.Message });
        }
    }

    /// <summary>
    /// Gets the status of a compression job
    /// </summary>
    [HttpGet("jobs/{jobId}/status")]
    public async Task<IActionResult> GetCompressionJobStatus(string jobId)
    {
        var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (userId == null) return Unauthorized();

        var job = await _context.CompressionJobs
            .Where(j => j.Id == jobId && j.UserId == userId)
            .FirstOrDefaultAsync();

        if (job == null)
        {
            return NotFound(new { message = "Compression job not found" });
        }

        return Ok(new CompressionJobResponse
        {
            JobId = job.Id,
            Status = job.Status.ToString(),
            Message = GetStatusMessage(job.Status),
            CreatedAt = job.CreatedAt,
            CompletedAt = job.CompletedAt,
            CompressionRatio = job.CompressionRatio,
            ErrorMessage = job.ErrorMessage
        });
    }

    /// <summary>
    /// Gets all compression jobs for the current user
    /// </summary>
    [HttpGet("jobs")]
    public async Task<IActionResult> GetCompressionJobs()
    {
        var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (userId == null) return Unauthorized();

        var jobs = await _context.CompressionJobs
            .Where(j => j.UserId == userId)
            .OrderByDescending(j => j.CreatedAt)
            .Take(50) // Limit to last 50 jobs
            .Select(j => new CompressionJobResponse
            {
                JobId = j.Id,
                Status = j.Status.ToString(),
                Message = GetStatusMessage(j.Status),
                CreatedAt = j.CreatedAt,
                CompletedAt = j.CompletedAt,
                CompressionRatio = j.CompressionRatio,
                ErrorMessage = j.ErrorMessage
            })
            .ToListAsync();

        return Ok(jobs);
    }

    private static string GetStatusMessage(CompressionJobStatus status)
    {
        return status switch
        {
            CompressionJobStatus.Queued => "Queued for processing",
            CompressionJobStatus.DownloadingFromGooglePhotos => "Downloading from Google Photos",
            CompressionJobStatus.UploadingToStorage => "Uploading to cloud storage",
            CompressionJobStatus.TranscodingInProgress => "Video transcoding in progress",
            CompressionJobStatus.CompressingImage => "Image compression in progress",
            CompressionJobStatus.DownloadingFromStorage => "Downloading compressed media",
            CompressionJobStatus.UploadingToGooglePhotos => "Uploading to Google Photos",
            CompressionJobStatus.DeletingOriginal => "Deleting original media",
            CompressionJobStatus.Completed => "Compression completed successfully",
            CompressionJobStatus.Failed => "Compression failed",
            CompressionJobStatus.Cancelled => "Compression was cancelled",
            _ => "Unknown status"
        };
    }
}

using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using System.Text;
using VidCompressor.Models;
using VidCompressor.Services;
using VidCompressor.Middleware;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddDbContext<ApplicationDbContext>(options =>
    options.UseNpgsql(builder.Configuration.GetConnectionString("DefaultConnection")));

builder.Services.AddAuthentication(options =>
{
    options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
})
.AddJwtBearer(options =>
{
    options.TokenValidationParameters = new TokenValidationParameters
    {
        ValidateIssuerSigningKey = true,
        IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(builder.Configuration["Jwt:Secret"] ?? throw new InvalidOperationException("JWT Secret not configured"))),
        ValidateIssuer = false,
        ValidateAudience = false
    };
});

// Configure Google Cloud settings
builder.Services.Configure<VidCompressor.Services.GoogleCloudConfig>(builder.Configuration.GetSection("GoogleCloud"));

builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowFrontend",
        builder =>
        {
            builder.WithOrigins("http://localhost:3000") // URL of the React app
                   .AllowAnyHeader()
                   .AllowAnyMethod()
                   .AllowCredentials(); // Required for SignalR
        });
});

builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

builder.Services.AddHttpClient();

builder.Services.AddScoped<GooglePhotosService>();
builder.Services.AddScoped<GoogleCloudStorageService>();
builder.Services.AddScoped<GoogleTranscoderService>();
builder.Services.AddScoped<CloudTasksService>();
builder.Services.AddScoped<ImageCompressionService>();
builder.Services.AddScoped<CompressionBackgroundService>();
builder.Services.AddHostedService<CompressionBackgroundService>();
builder.Services.AddSignalR();

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

// Add global exception handling
app.UseMiddleware<GlobalExceptionMiddleware>();

app.UseHttpsRedirection();

app.UseCors("AllowFrontend");

app.UseAuthentication();
app.UseAuthorization();

app.MapControllers();
app.MapHub<VidCompressor.Hubs.NotificationHub>("/notificationHub");

app.Run();

public class ApplicationDbContext : DbContext
{
    public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : base(options)
    {
    }

    public DbSet<User> Users { get; set; }
    public DbSet<CompressionJob> CompressionJobs { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Configure User-CompressionJob relationship
        modelBuilder.Entity<CompressionJob>()
            .HasOne(cj => cj.User)
            .WithMany(u => u.CompressionJobs)
            .HasForeignKey(cj => cj.UserId)
            .OnDelete(DeleteBehavior.Cascade);

        // Configure indexes for better query performance
        modelBuilder.Entity<CompressionJob>()
            .HasIndex(cj => cj.UserId);

        modelBuilder.Entity<CompressionJob>()
            .HasIndex(cj => cj.Status);

        modelBuilder.Entity<CompressionJob>()
            .HasIndex(cj => cj.CreatedAt);
    }
}
